<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态时间线测试</title>
    <link rel="stylesheet" href="assets/css/timeline.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>动态时间线测试</h1>
        <button class="test-button" onclick="testTimeline()">测试动态时间线</button>
        <button class="test-button" onclick="testAPI()">测试API数据</button>
        
        <div id="api-result" style="margin-bottom: 20px;"></div>
        
        <div class="timeline-container" id="timeline-container">
            <div class="timeline-loading">点击按钮测试时间线...</div>
        </div>
    </div>

    <!-- 图片放大模态框 -->
    <div id="imageModal" class="image-modal">
        <div class="image-modal-content">
            <span class="image-modal-close">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
            <div id="modalCaption" class="modal-caption"></div>
        </div>
    </div>

    <script>
        // 测试API数据获取
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p>正在获取API数据...</p>';
            
            try {
                const response = await fetch('http://localhost:3000/api');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = '<p style="color: green;">✅ API数据获取成功</p>';
                    console.log('API数据:', result.data);
                    
                    // 自动生成时间线
                    generateDynamicTimeline(result.data);
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">❌ API调用失败: ' + result.error + '</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<p style="color: red;">❌ 网络错误: ' + error.message + '</p>';
                console.error('API调用错误:', error);
            }
        }

        // 测试时间线生成
        function testTimeline() {
            const testData = {
                LifeCycleNodes: [
                    {
                        "NodeName": "产品信息",
                        "Properties": [
                            {"PropertyName": "产品信息", "PropertyType": 1},
                            {"PropertyName": "批号", "PropertyType": 1},
                            {"PropertyName": "产地", "PropertyType": 1}
                        ]
                    },
                    {
                        "NodeName": "检验信息",
                        "Properties": [
                            {"PropertyName": "原药材检测", "PropertyType": 7},
                            {"PropertyName": "成品检测", "PropertyType": 7}
                        ]
                    }
                ],
                LifeCycles: {
                    "产品信息_开始时间": "2025.08.01",
                    "产品信息_产品信息": "芡实",
                    "产品信息_批号": "25080101A",
                    "产品信息_产地": "广东省肇庆市",
                    "检验信息_开始时间": "2025.07.23",
                    "检验信息_原药材检测": "688dd2645545dbf1f62328c5",
                    "检验信息_成品检测": "688dd26b5545dbf1f62328c7"
                },
                EntCode: "05580002"
            };
            
            generateDynamicTimeline(testData);
        }

        // 动态生成时间线的函数
        function generateDynamicTimeline(apiData) {
            try {
                const timelineContainer = document.getElementById('timeline-container');
                if (!timelineContainer) {
                    console.error('找不到时间线容器');
                    return;
                }

                // 获取生命周期节点和数据
                const lifeCycleNodes = apiData.LifeCycleNodes || [];
                const lifeCycles = apiData.LifeCycles || {};
                const entCode = apiData.EntCode || '05580002';

                console.log('LifeCycleNodes:', lifeCycleNodes);
                console.log('LifeCycles:', lifeCycles);

                // 清空现有内容
                timelineContainer.innerHTML = '';

                if (lifeCycleNodes.length === 0) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">暂无时间线数据</div>';
                    return;
                }

                // 遍历每个生命周期节点
                lifeCycleNodes.forEach((node, index) => {
                    const nodeName = node.NodeName || `节点${index + 1}`;
                    const properties = node.Properties || [];

                    // 获取开始时间
                    const startTimeKey = `${nodeName}_开始时间`;
                    const startTime = lifeCycles[startTimeKey] || '未知时间';

                    // 创建时间线项目
                    const timelineItem = document.createElement('div');
                    timelineItem.className = 'timeline-item';
                    timelineItem.id = `timeline-${nodeName.replace(/\s+/g, '-')}`;

                    // 创建时间线日期
                    const timelineDate = document.createElement('div');
                    timelineDate.className = 'timeline-date';
                    timelineDate.innerHTML = `<span>${startTime}</span> ${nodeName}`;

                    // 创建时间线内容
                    const timelineContent = document.createElement('div');
                    timelineContent.className = 'timeline-content';

                    const timelineDetails = document.createElement('div');
                    timelineDetails.className = 'timeline-details';

                    // 遍历属性
                    properties.forEach(property => {
                        const propertyName = property.PropertyName || '';
                        const propertyType = property.PropertyType || 0;

                        if (!propertyName) return;

                        // 构建查找键：NodeName_PropertyName
                        const lookupKey = `${nodeName}_${propertyName}`;
                        const value = lifeCycles[lookupKey];

                        if (!value) return; // 如果没有值，跳过

                        // 创建详情行
                        const detailRow = document.createElement('div');
                        detailRow.className = 'detail-row';

                        const detailLabel = document.createElement('span');
                        detailLabel.className = 'detail-label';
                        detailLabel.textContent = `${propertyName}：`;

                        const detailValue = document.createElement('span');
                        detailValue.className = 'detail-value';

                        // 根据属性类型处理值
                        if (propertyType === 7) {
                            // 图片类型
                            const img = document.createElement('img');
                            img.src = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${value}`;
                            img.alt = propertyName;
                            img.style.maxWidth = '200px';
                            img.style.maxHeight = '150px';
                            img.style.objectFit = 'contain';
                            img.style.border = '1px solid #ddd';
                            img.style.borderRadius = '4px';
                            img.style.marginTop = '5px';
                            img.style.cursor = 'pointer';
                            
                            // 添加点击放大功能
                            img.onclick = function() {
                                showImageModal(this.src, propertyName);
                            };
                            
                            detailValue.appendChild(img);
                        } else {
                            // 文本类型
                            detailValue.textContent = value;
                        }

                        detailRow.appendChild(detailLabel);
                        detailRow.appendChild(detailValue);
                        timelineDetails.appendChild(detailRow);
                    });

                    timelineContent.appendChild(timelineDetails);
                    timelineItem.appendChild(timelineDate);
                    timelineItem.appendChild(timelineContent);
                    timelineContainer.appendChild(timelineItem);
                });

                console.log('✅ 动态时间线已生成，共', lifeCycleNodes.length, '个节点');

            } catch (error) {
                console.error('生成动态时间线时发生错误:', error);
                const timelineContainer = document.getElementById('timeline-container');
                if (timelineContainer) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">时间线生成失败: ' + error.message + '</div>';
                }
            }
        }
    </script>
</body>
</html>
