<!DOCTYPE html>
<html lang="zh-Hans">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--=============== FAVICON ===============-->
    <link rel="shortcut icon" href="./assets/img/favicon.png" type="image/x-icon">

    <!--=============== BOXICONS ===============-->
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>

    <!--=============== SWIPER CSS ===============-->
    <link rel="stylesheet" href="assets/css/swiper-bundle.min.css">
    
    <!--=============== CSS ===============-->
    <link rel="stylesheet" href="assets/css/styles.css">
    
    <!--=============== TIMELINE CSS ===============-->
    <link rel="stylesheet" href="assets/css/timeline.css">

    <title id="page-title">加载中...</title>
</head>

<body>
    <!--=============== HEADER ===============-->
    <header class="header" id="header">
        <nav class="nav container">
            <a href="#" class="nav__logo" id="nav-logo">加载中...</a>
            <!-- 导航菜单 -->
            <div class="nav__menu">
                <ul class="nav__list">
                    <li class="nav__item">
                        <a href="#home" class="nav__link active-link">
                            <i class='bx bx-home-heart'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#about" class="nav__link">
                            <i class='bx bx-user'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#skills " class="nav__link">
                            <i class='bx bx-book-heart'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#work" class="nav__link">
                            <i class='bx bx-briefcase-alt-2'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#contact" class="nav__link">
                            <i class='bx bx-message-square-detail'></i>
                        </a>
                    </li>
                </ul>
            </div>
            <!-- 主题切换按钮 -->
            <i class='bx bx-moon change-theme' id="theme-button"></i>
        </nav>
    </header>

    <!--=============== MAIN ===============-->
    <main class="main">
        <!--=============== HOME ===============-->
        <section class="home section" id="home">
            <div class="home__container container grid">
                <div class="home__carousel">
                    <div class="carousel-container">
                        <div class="carousel-slides" id="carousel-slides">
                            <!-- 轮播图将通过JavaScript动态生成 -->
                            <div class="carousel-loading">加载中...</div>
                        </div>
                        <div class="carousel-dots" id="carousel-dots">
                            <!-- 圆点将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== ABOUT ===============-->
        <section class="about section" id="about">
            <div class="about__container grid">
                <div class="about__expandable">
                    <div class="expandable__item">
                        <div class="expandable__header" data-target="product-info">
                            <h3 class="expandable__title">产品信息</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="product-info">
                            <div class="expandable__body">
                                <div class="product-info-form">
                                    <div class="form-row">
                                        <label class="form-label">品名</label>
                                        <input type="text" class="form-input" id="product-name" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">规格</label>
                                        <input type="text" class="form-input" id="product-spec" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">产地</label>
                                        <input type="text" class="form-input" id="product-origin" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">执行标准</label>
                                        <input type="text" class="form-input" id="product-standard" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">包装规格</label>
                                        <input type="text" class="form-input" id="product-package" value="加载中..." readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="production-trace">
                            <h3 class="expandable__title">生产追溯</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="production-trace">
                            <div class="expandable__body">
                                <!-- 生产追溯内容 -->
                                <div class="production-trace-container">
                                    <!-- 基本信息卡片 -->
                                    <div class="trace-info-card">
                                        <div class="form-row">
                                            <label class="form-label">生产批号</label>
                                            <input type="text" class="form-input" id="production-batch-no" value="加载中..." readonly>
                                        </div>
                                        <div class="form-row">
                                            <label class="form-label">生产日期</label>
                                            <input type="text" class="form-input" id="production-date" value="加载中..." readonly>
                                        </div>
                                        <div class="form-row">
                                            <label class="form-label">保质期</label>
                                            <input type="text" class="form-input" id="expiration-date" value="加载中..." readonly>
                                        </div>
                                    </div>

                                    <!-- 时间线 -->
                                    <div class="timeline-container" id="timeline-container">
                                        <!-- 时间线将通过JavaScript动态生成 -->
                                        <div class="timeline-loading">加载中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="company-intro">
                            <h3 class="expandable__title">企业介绍</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="company-intro">
                            <div class="expandable__body">
                                <!-- 企业介绍内容将在这里显示 -->
                                <p>企业介绍详细内容...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== SKILLS ===============-->
        <section class="skills section" id="skills">
            <span class="section__subtitle">我都会哪些？</span>
            <h2 class="section__title">我掌握的技术</h2>

            <div class="skills__container container grid">

                <div class="skills__content">
                    <h3 class="skills__title">前端开发</h3>

                    <div class="skills__box">
                        <!-- 技能树1 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">HTML</h3>
                                    <span class="skills__level">熟练</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">CSS</h3>
                                    <span class="skills__level">熟练</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">JavaScript</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                        <!-- 技能树2 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Vue2/Vue3</h3>
                                    <span class="skills__level">入门</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">微信小程序</h3>
                                    <span class="skills__level">入门</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">ElementUI</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="skills__content">
                    <h3 class="skills__title">后端开发</h3>

                    <div class="skills__box">
                        <!-- 技能树1 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Java</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Pyhton</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Node Js</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                        <!-- 技能树2 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">MySQL</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">微信小程序云开发</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Firebase</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== SERVICES ===============-->
        <section class="services section">
            <span class="section__subtitle">我能做什么？</span>
            <h2 class="section__title">我可以运用这些技术</h2>

            <div class="services__container container grid">
                <div class="services__card">
                    <h3 class="services__title">网页设计与制作</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">网页设计与制作</h3>
                            <p class="services__modal-description">
                                xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="services__card">
                    <h3 class="services__title">Vue项目开发</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">Vue项目开发</h3>
                            <p class="services__modal-description">
                                xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="services__card">
                    <h3 class="services__title">小程序开发</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">小程序开发</h3>
                            <p class="services__modal-description">
                                我说这是嘛呀xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        一个xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        两个xxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== WORK ===============-->
        <section class="work section" id="work">
            <span class="section__subtitle">我做过哪些？</span>
            <h2 class="section__title">我最近的项目</h2>

            <div class="work__filters">
                <span class="work__item active-work" data-filter="all">全部</span>
                <span class="work__item" data-filter=".web">网页</span>
                <span class="work__item" data-filter=".mini">小程序</span>
                <span class="work__item" data-filter=".admin">后台管理系统</span>
            </div>

            <div class="work__container container grid">
                <div class="work__card mix web">
                    <img src="assets/img/work1.jpg" alt="" class="work__img">
                    <h3 class="work__title">网页</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix mini">
                    <img src="assets/img/work2.jpg" alt="" class="work__img">
                    <h3 class="work__title">小程序</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix mini">
                    <img src="assets/img/work3.jpg" alt="" class="work__img">
                    <h3 class="work__title">小程序</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix web">
                    <img src="assets/img/work4.jpg" alt="" class="work__img">
                    <h3 class="work__title">网页</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix admin">
                    <img src="assets/img/work5.jpg" alt="" class="work__img">
                    <h3 class="work__title">后台管理系统</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
            </div>
        </section>

        <!--=============== TESTIMONIALS ===============-->
        <section class="testimonial section">
            <span class="section__subtitle">认识我的人是这么说的</span>
            <h2 class="section__title">我的评价</h2>

            <div class="testimonial__container container swiper">
                <div class=" swiper-wrapper">
                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial1.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">舍友陈同学</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>

                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial2.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">辅导员张老师</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>

                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial3.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">同事小林</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </section>

        <!--=============== CONTACT ===============-->
        <section class="contact section" id="contact">
            <span class="section__subtitle">如何与我联系？</span>
            <h2 class="section__title">我的联系方式</h2>

            <div class="contact__container container grid">
                <div class="contactt__content">
                    <h3 class="contactt__title">与我交流</h3>

                    <div class="contact__info">
                        <div class="contact__card">
                            <i class='bx bx-phone-call contact__card-icon'></i>
                            <h3 class="contact__card-title">电话</h3>
                            <span class="contact__card-data">123456789</span>

                            <a href="" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                        <div class="contact__card">
                            <i class='bx bx-mail-send contact__card-icon'></i>
                            <h3 class="contact__card-title">邮箱</h3>
                            <span class="contact__card-data"><EMAIL></span>

                            <a href="mailto:<EMAIL>" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                        <div class="contact__card">
                            <i class='bx bxl-whatsapp contact__card-icon'></i>
                            <h3 class="contact__card-title">微信</h3>
                            <span class="contact__card-data">ImPump_</span>

                            <a href="" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                    </div>
                </div>
        </section>
    </main>

    <!--=============== IMAGE MODAL ===============-->
    <div id="imageModal" class="image-modal">
        <div class="image-modal-content">
            <span class="image-modal-close">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
            <div id="modalCaption" class="modal-caption"></div>
        </div>
    </div>

    <!--=============== FOOTER ===============-->
    <footer class="footer">
        <div class="footer__container container">
            <h1 class="footer__title">李晓鹏</h1>
            <ul class="footer__list">
                <li>
                    <a href="#about" class="footer__link">我的信息</a>
                </li>
                <li>
                    <a href="#skills" class="footer__link">我的技能</a>
                </li>
                <li>
                    <a href="#work" class="footer__link">我的项目</a>
                </li>
            </ul>

            <ul class="footer__social">
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-facebook'></i>
                </a>
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-instagram'></i>
                </a>
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-twitter' ></i>
                </a>
            </ul>

            <span class="footer__copy">
                &#169;ImPump. All rights reserved
            </span>
        </div>
    </footer>

    <!--=============== SCROLLREVEAL ===============-->
    <script src="assets/js/scrollreveal.min.js"></script>

    <!--=============== SWIPER JS ===============-->
    <script src="assets/js/swiper-bundle.min.js"></script>

    <!--=============== MIXITUP FILTER ===============-->
    <script src="assets/js/mixitup.min.js"></script>

    <!--=============== MAIN JS ===============-->
    <script src="assets/js/main.js"></script>

    <!--=============== API CALL SCRIPT ===============-->
    <script>
        // 更新页面标题的函数
        function updatePageTitle(apiData) {
            try {
                const entName = apiData.EntName || '未知企业';

                // 更新页面标题
                const pageTitleElement = document.getElementById('page-title');
                if (pageTitleElement) {
                    pageTitleElement.textContent = entName;
                }

                // 更新导航栏logo
                const navLogoElement = document.getElementById('nav-logo');
                if (navLogoElement) {
                    navLogoElement.textContent = entName;
                }

                console.log('✅ 页面标题已更新:', entName);

            } catch (error) {
                console.error('更新页面标题时发生错误:', error);
                const pageTitleElement = document.getElementById('page-title');
                const navLogoElement = document.getElementById('nav-logo');
                if (pageTitleElement) pageTitleElement.textContent = '加载失败';
                if (navLogoElement) navLogoElement.textContent = '加载失败';
            }
        }

        // 更新轮播图的函数
        function updateCarousel(apiData) {
            try {
                const carouselImages = apiData.CarouselImages || [];
                const entCode = apiData.EntCode || '05580002';

                console.log('轮播图数据:', carouselImages);
                console.log('企业代码:', entCode);

                const carouselSlidesContainer = document.getElementById('carousel-slides');
                const carouselDotsContainer = document.getElementById('carousel-dots');

                if (!carouselSlidesContainer || !carouselDotsContainer) {
                    console.error('找不到轮播图容器元素');
                    return;
                }

                // 清空现有内容
                carouselSlidesContainer.innerHTML = '';
                carouselDotsContainer.innerHTML = '';

                if (carouselImages.length === 0) {
                    carouselSlidesContainer.innerHTML = '<div class="carousel-loading">暂无轮播图</div>';
                    return;
                }

                // 生成轮播图片
                carouselImages.forEach((imageId, index) => {
                    const imageUrl = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${imageId}`;

                    const imgElement = document.createElement('img');
                    imgElement.src = imageUrl;
                    imgElement.alt = `轮播图${index + 1}`;
                    imgElement.className = 'carousel-img';
                    if (index === 0) {
                        imgElement.classList.add('active');
                    }

                    carouselSlidesContainer.appendChild(imgElement);

                    // 生成对应的圆点
                    const dotElement = document.createElement('span');
                    dotElement.className = 'dot';
                    dotElement.setAttribute('data-slide', index.toString());
                    if (index === 0) {
                        dotElement.classList.add('active');
                    }

                    carouselDotsContainer.appendChild(dotElement);
                });

                // 重新初始化轮播功能
                initializeCarousel();

                console.log('✅ 轮播图已更新，共', carouselImages.length, '张图片');

            } catch (error) {
                console.error('更新轮播图时发生错误:', error);
                const carouselSlidesContainer = document.getElementById('carousel-slides');
                if (carouselSlidesContainer) {
                    carouselSlidesContainer.innerHTML = '<div class="carousel-loading">轮播图加载失败</div>';
                }
            }
        }

        // 初始化轮播功能
        function initializeCarousel() {
            let currentSlide = 0;
            const slides = document.querySelectorAll('.carousel-img');
            const dots = document.querySelectorAll('.dot');
            const totalSlides = slides.length;

            if (totalSlides === 0) return;

            function showSlide(index) {
                // 移除所有active类
                slides.forEach(slide => slide.classList.remove('active'));
                dots.forEach(dot => dot.classList.remove('active'));

                // 添加active类到当前幻灯片和点
                if (slides[index]) slides[index].classList.add('active');
                if (dots[index]) dots[index].classList.add('active');
            }

            function nextSlide() {
                currentSlide = (currentSlide + 1) % totalSlides;
                showSlide(currentSlide);
            }

            // 点击圆点切换图片
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentSlide = index;
                    showSlide(currentSlide);
                });
            });

            // 清除之前的定时器（如果存在）
            if (window.carouselInterval) {
                clearInterval(window.carouselInterval);
            }

            // 自动轮播，每3秒切换一次
            window.carouselInterval = setInterval(nextSlide, 3000);
        }

        // 更新页面产品信息的函数
        function updateProductInfo(apiData) {
            try {
                // 获取产品名称
                const productName = apiData.Product || '未知产品';

                // 获取产品属性
                const productProperties = apiData.ProductProperties || {};

                // 更新页面元素
                const productNameElement = document.getElementById('product-name');
                const productSpecElement = document.getElementById('product-spec');
                const productOriginElement = document.getElementById('product-origin');
                const productStandardElement = document.getElementById('product-standard');
                const productPackageElement = document.getElementById('product-package');

                if (productNameElement) {
                    productNameElement.value = productName;
                }

                if (productSpecElement) {
                    productSpecElement.value = productProperties['基础信息_规格'] || '未知规格';
                }

                if (productOriginElement) {
                    productOriginElement.value = productProperties['基础信息_产地'] || '未知产地';
                }

                if (productStandardElement) {
                    productStandardElement.value = productProperties['基础信息_执行标准'] || '未知标准';
                }

                if (productPackageElement) {
                    productPackageElement.value = productProperties['基础信息_包装规格'] || '未知包装';
                }

                console.log('✅ 页面产品信息已更新');
                console.log('品名:', productName);
                console.log('规格:', productProperties['基础信息_规格']);
                console.log('产地:', productProperties['基础信息_产地']);
                console.log('执行标准:', productProperties['基础信息_执行标准']);
                console.log('包装规格:', productProperties['基础信息_包装规格']);

            } catch (error) {
                console.error('更新产品信息时发生错误:', error);
                // 如果更新失败，显示错误信息
                const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '加载失败';
                    }
                });
            }
        }

        // 更新生产追溯信息的函数
        function updateProductionTrace(apiData) {
            try {
                // 更新基本信息卡片
                const batchNoElement = document.getElementById('production-batch-no');
                const productionDateElement = document.getElementById('production-date');
                const expirationDateElement = document.getElementById('expiration-date');

                if (batchNoElement) {
                    batchNoElement.value = apiData.ProductionBatchNo || '未知批号';
                }

                if (productionDateElement) {
                    productionDateElement.value = apiData.ProductionDate || '未知日期';
                }

                if (expirationDateElement) {
                    expirationDateElement.value = apiData.ExpirationDate || '未知日期';
                }

                // 动态生成时间线
                generateDynamicTimeline(apiData);

                console.log('✅ 生产追溯信息已更新');
                console.log('生产批号:', apiData.ProductionBatchNo);
                console.log('生产日期:', apiData.ProductionDate);
                console.log('保质期:', apiData.ExpirationDate);

            } catch (error) {
                console.error('更新生产追溯信息时发生错误:', error);
                // 如果更新失败，显示错误信息
                const traceElements = ['production-batch-no', 'production-date', 'expiration-date'];
                traceElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '加载失败';
                    }
                });
            }
        }

        // 图片放大模态框功能
        function showImageModal(imageSrc, caption) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const modalCaption = document.getElementById('modalCaption');

            modal.style.display = 'block';
            modalImg.src = imageSrc;
            modalCaption.textContent = caption || '';

            // 添加淡入动画
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        function hideImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');

            // 等待动画完成后隐藏
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 初始化图片模态框事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('imageModal');
            const closeBtn = document.querySelector('.image-modal-close');

            // 点击关闭按钮
            if (closeBtn) {
                closeBtn.onclick = hideImageModal;
            }

            // 点击模态框背景关闭
            if (modal) {
                modal.onclick = function(event) {
                    if (event.target === modal) {
                        hideImageModal();
                    }
                };
            }

            // ESC键关闭
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape' && modal.style.display === 'block') {
                    hideImageModal();
                }
            });
        });

        // 动态生成时间线的函数
        function generateDynamicTimeline(apiData) {
            try {
                const timelineContainer = document.getElementById('timeline-container');
                if (!timelineContainer) {
                    console.error('找不到时间线容器');
                    return;
                }

                // 获取生命周期节点和数据
                const lifeCycleNodes = apiData.LifeCycleNodes || [];
                const lifeCycles = apiData.LifeCycles || {};
                const entCode = apiData.EntCode || '05580002';

                console.log('LifeCycleNodes:', lifeCycleNodes);
                console.log('LifeCycles:', lifeCycles);

                // 清空现有内容
                timelineContainer.innerHTML = '';

                if (lifeCycleNodes.length === 0) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">暂无时间线数据</div>';
                    return;
                }

                // 遍历每个生命周期节点
                lifeCycleNodes.forEach((node, index) => {
                    const nodeName = node.NodeName || `节点${index + 1}`;
                    const properties = node.Properties || [];

                    // 获取开始时间
                    const startTimeKey = `${nodeName}_开始时间`;
                    const startTime = lifeCycles[startTimeKey] || '未知时间';

                    // 创建时间线项目
                    const timelineItem = document.createElement('div');
                    timelineItem.className = 'timeline-item';
                    timelineItem.id = `timeline-${nodeName.replace(/\s+/g, '-')}`;

                    // 创建时间线日期
                    const timelineDate = document.createElement('div');
                    timelineDate.className = 'timeline-date';
                    timelineDate.innerHTML = `<span>${startTime}</span> ${nodeName}`;

                    // 创建时间线内容
                    const timelineContent = document.createElement('div');
                    timelineContent.className = 'timeline-content';

                    const timelineDetails = document.createElement('div');
                    timelineDetails.className = 'timeline-details';

                    // 遍历属性
                    properties.forEach(property => {
                        const propertyName = property.PropertyName || '';
                        const propertyType = property.PropertyType || 0;

                        if (!propertyName) return;

                        // 构建查找键：NodeName_PropertyName
                        const lookupKey = `${nodeName}_${propertyName}`;
                        const value = lifeCycles[lookupKey];

                        if (!value) return; // 如果没有值，跳过

                        // 创建详情行
                        const detailRow = document.createElement('div');
                        detailRow.className = 'detail-row';

                        const detailLabel = document.createElement('span');
                        detailLabel.className = 'detail-label';
                        detailLabel.textContent = `${propertyName}：`;

                        const detailValue = document.createElement('span');
                        detailValue.className = 'detail-value';

                        // 根据属性类型处理值
                        if (propertyType === 7) {
                            // 图片类型
                            const img = document.createElement('img');
                            img.src = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${value}`;
                            img.alt = propertyName;
                            img.style.maxWidth = '200px';
                            img.style.maxHeight = '150px';
                            img.style.objectFit = 'contain';
                            img.style.border = '1px solid #ddd';
                            img.style.borderRadius = '4px';
                            img.style.marginTop = '5px';
                            img.style.cursor = 'pointer';

                            // 添加点击放大功能
                            img.onclick = function() {
                                showImageModal(this.src, propertyName);
                            };

                            // 添加加载错误处理
                            img.onerror = function() {
                                this.style.display = 'none';
                                const errorText = document.createElement('span');
                                errorText.textContent = '图片加载失败';
                                errorText.style.color = '#999';
                                errorText.style.fontStyle = 'italic';
                                detailValue.appendChild(errorText);
                            };

                            detailValue.appendChild(img);
                        } else {
                            // 文本类型
                            detailValue.textContent = value;
                        }

                        detailRow.appendChild(detailLabel);
                        detailRow.appendChild(detailValue);
                        timelineDetails.appendChild(detailRow);
                    });

                    timelineContent.appendChild(timelineDetails);
                    timelineItem.appendChild(timelineDate);
                    timelineItem.appendChild(timelineContent);
                    timelineContainer.appendChild(timelineItem);
                });

                console.log('✅ 动态时间线已生成，共', lifeCycleNodes.length, '个节点');

            } catch (error) {
                console.error('生成动态时间线时发生错误:', error);
                const timelineContainer = document.getElementById('timeline-container');
                if (timelineContainer) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">时间线生成失败</div>';
                }
            }
        }

        // 调用昆鹏360接口的函数（通过本地代理服务器）
        async function callKunpengAPI() {
            const proxyUrl = 'http://localhost:3000/api';

            try {
                console.log('正在通过代理服务器调用API:', proxyUrl);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                console.log('代理服务器响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('=== API调用结果 ===');
                console.log('完整响应:', result);

                if (result.success) {
                    console.log('=== 原始API返回数据 ===');
                    console.log(result.rawData);

                    console.log('=== 解析后的数据 ===');
                    console.log(result.data);

                    console.log('=== API响应状态码 ===');
                    console.log(result.statusCode);

                    console.log('=== API响应头 ===');
                    console.log(result.headers);

                    // 🎯 关键：更新页面所有信息
                    updatePageTitle(result.data);
                    updateCarousel(result.data);
                    updateProductInfo(result.data);
                    updateProductionTrace(result.data);

                } else {
                    console.error('API调用失败:', result.error);
                    // 显示错误状态
                    const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                    elements.forEach(id => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.value = 'API调用失败';
                        }
                    });
                }

                return result;

            } catch (error) {
                console.error('调用代理服务器时发生错误:', error);
                console.error('错误详情:', error.message);
                console.log('请确保代理服务器正在运行 (node api-server.js)');

                // 显示错误状态
                const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '连接失败';
                    }
                });

                return null;
            }
        }

        // 页面加载完成后自动调用API
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始调用API...');

            // 调用真实API
            callKunpengAPI().then(result => {
                if (!result || !result.success) {
                    console.error('API调用失败，无法获取数据');
                    // 显示错误状态
                    showErrorState('API调用失败');
                }
            }).catch(error => {
                console.error('API调用出错:', error);
                showErrorState('连接失败');
            });
        });

        // 显示错误状态的函数
        function showErrorState(errorMessage) {
            const elements = [
                'product-name', 'product-spec', 'product-origin',
                'product-standard', 'product-package', 'production-batch-no',
                'production-date', 'expiration-date'
            ];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = errorMessage;
                }
            });
        }

        // 也可以手动调用
        window.callKunpengAPI = callKunpengAPI;
    </script>
</body>

</html>